<div class="feature-grid">
  <div class="feature-item">
    <img src="https://luxurywatchesusa.com/wp-content/uploads/2022/11/secure-shield-shield-svgrepo-com.svg" alt="2 Years Warranty" />
    <span>2 Years Warranty</span>
  </div>
  <div class="feature-item">
    <img src="https://luxurywatchesusa.com/wp-content/uploads/2022/11/bank-transfer-svgrepo-com.svg" alt="Wire Transfer Discount" />
    <span>Wire Transfer Discount</span>
  </div>
  <div class="feature-item">
    <img src="https://luxurywatchesusa.com/wp-content/uploads/2022/11/secure-payment-svgrepo-com.svg" alt="Secure Payment" />
    <span>Secure Payment</span>
  </div>
  <div class="feature-item">
    <img src="https://luxurywatchesusa.com/wp-content/uploads/2022/11/achievement-award-medal-svgrepo-com.svg" alt="Certified Authentic" />
    <span>Certified Authentic</span>
  </div>
</div>

<div class="custom-actions-block">
  <a href="#" class="whatsapp-button" id="whatsapp-link" target="_blank" rel="noopener noreferrer">
    <svg class="whatsapp-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
      <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 221.9-99.6 221.9-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.8 0-67.6-9.5-97.2-27.2l-6.9-4.1-72.3 19 19.3-70.4-4.5-7.2c-19.1-30.1-29.2-65.4-29.2-101.9 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
    </svg>
    <span>Chat on WhatsApp</span>
  </a>

  <a href="#" class="secondary-btn" id="callback-btn"><i class="fa-solid fa-phone-volume"></i> Request Callback</a>
  <a href="#" class="secondary-btn" id="offer-btn"><i class="fa-solid fa-tag"></i> Make an Offer</a>
  <a href="#" class="secondary-btn" id="sell-btn"><i class="fa-solid fa-hand-holding-dollar"></i> Sell This Watch</a>
</div>

<!-- Modal for Callback -->
<div id="callback-modal" class="modal">
  <div class="modal-content">
    <span class="close" data-modal="callback-modal">&times;</span>
    <div class="modal-body">
      [wpb-pcf-button id="callback-form"]
    </div>
  </div>
</div>

<!-- Modal for Make Offer -->
<div id="offer-modal" class="modal">
  <div class="modal-content">
    <span class="close" data-modal="offer-modal">&times;</span>
    <div class="modal-body">
      [wpb-pcf-button id="make-offer-form"]
    </div>
  </div>
</div>

<!-- Modal for Sell Watch -->
<div id="sell-modal" class="modal">
  <div class="modal-content">
    <span class="close" data-modal="sell-modal">&times;</span>
    <div class="modal-body">
      [wpb-pcf-button id="sell-watch-form"]
    </div>
  </div>
</div>

<style>
  .feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px 16px;
    margin-bottom: 24px;
  }
  .feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 17px;
    color: #333;
    font-weight: 500;
  }
  .feature-item img {
    width: 32px;
    height: 32px;
    object-fit: contain;
  }
  @media (max-width: 480px) {
    .feature-grid {
      grid-template-columns: 1fr;
    }
    .feature-item {
      font-size: 14px;
    }
    .feature-item img {
      width: 24px;
      height: 24px;
    }
  }

  .custom-actions-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  }

  .custom-actions-block .secondary-btn {
    color: #00529b;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    transition: background-color 0.2s ease, color 0.2s ease;
    line-height: 1.4;
  }

  .custom-actions-block .secondary-btn:hover {
    background-color: #f8f9fa;
    color: #1a1a1a;
    text-decoration: none;
  }

  .custom-actions-block .secondary-btn i {
    font-size: 16px;
  }

  .custom-actions-block .whatsapp-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 24px;
    background-color: #fff;
    color: #25D366;
    border: 2px solid #25D366;
    border-radius: 3px;
    text-decoration: none;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: pulse-glow-custom 2s infinite ease-in-out;
    height: 100%;
    box-sizing: border-box;
  }

  .custom-actions-block .whatsapp-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    fill: #25D366;
    transition: fill 0.3s ease;
  }

  @keyframes pulse-glow-custom {
    0% {
      box-shadow: inset 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
      box-shadow: inset 0 0 0 5px rgba(37, 211, 102, 0);
    }
    100% {
      box-shadow: inset 0 0 0 0 rgba(37, 211, 102, 0);
    }
  }

  .custom-actions-block .whatsapp-button:hover {
    transform: translateY(-2px);
    background-color: #25D366;
    color: #fff;
    text-decoration: none;
  }

  .custom-actions-block .whatsapp-button:hover .whatsapp-icon {
    fill: #fff;
  }

  @media (max-width: 480px) {
    .custom-actions-block {
      grid-template-columns: 1fr;
    }
  }
</style>

<script>
document.addEventListener("DOMContentLoaded", function () {
  const phoneNumber = "16469798028";
  const pageUrl = window.location.href;

  // Simple message format as requested
  const message = `Hello, I want information about:

*From:* ${pageUrl}`;

  const encodedMessage = encodeURIComponent(message);
  const whatsappUrl = `https://api.whatsapp.com/send?phone=${phoneNumber}&text=${encodedMessage}`;

  const whatsappLink = document.getElementById("whatsapp-link");
  if (whatsappLink) {
    whatsappLink.setAttribute("href", whatsappUrl);

    // Add comprehensive tracking for GA4 and GTM
    whatsappLink.addEventListener("click", function () {

      // GA4 Enhanced Ecommerce Event
      if (typeof gtag === "function") {
        gtag("event", "whatsapp_contact", {
          event_category: "engagement",
          event_label: "whatsapp_button_click",
          page_url: pageUrl,
          contact_method: "whatsapp",
          phone_number: phoneNumber,
          custom_parameter_1: "button_click",
          value: 1
        });
      }

      // Google Tag Manager DataLayer Push
      if (typeof dataLayer !== "undefined") {
        dataLayer.push({
          event: "whatsapp_click",
          event_category: "contact",
          event_action: "whatsapp_button_click",
          event_label: pageUrl,
          contact_method: "whatsapp",
          phone_number: phoneNumber,
          page_url: pageUrl,
          timestamp: new Date().toISOString()
        });
      }

      // Fallback console logging for debugging
      console.log("WhatsApp Contact Event:", {
        action: "whatsapp_click",
        page: pageUrl,
        phone: phoneNumber,
        timestamp: new Date().toISOString()
      });
    });
  }

  // Handle other action buttons
  const callbackBtn = document.getElementById("callback-btn");
  const offerBtn = document.getElementById("offer-btn");
  const sellBtn = document.getElementById("sell-btn");

  // Request Callback button
  if (callbackBtn) {
    callbackBtn.addEventListener("click", function(e) {
      e.preventDefault();

      // Open callback form in new tab with WordPress shortcode
      window.open('/callback-form?shortcode=63860', '_blank');

      // Track callback request
      if (typeof gtag === "function") {
        gtag("event", "callback_request", {
          event_category: "engagement",
          event_label: "callback_button_click",
          page_url: pageUrl,
          value: 1
        });
      }

      if (typeof dataLayer !== "undefined") {
        dataLayer.push({
          event: "callback_request",
          event_category: "contact",
          event_action: "callback_button_click",
          event_label: pageUrl,
          page_url: pageUrl
        });
      }
    });
  }

  // Make an Offer button
  if (offerBtn) {
    offerBtn.addEventListener("click", function(e) {
      e.preventDefault();

      // Open offer form in new tab
      window.open('/make-offer?shortcode=63859', '_blank');

      // Track offer click
      if (typeof gtag === "function") {
        gtag("event", "offer_click", {
          event_category: "engagement",
          event_label: "offer_button_click",
          page_url: pageUrl,
          value: 1
        });
      }

      if (typeof dataLayer !== "undefined") {
        dataLayer.push({
          event: "offer_click",
          event_category: "engagement",
          event_action: "offer_button_click",
          event_label: pageUrl,
          page_url: pageUrl
        });
      }
    });
  }

  // Sell This Watch button
  if (sellBtn) {
    sellBtn.addEventListener("click", function(e) {
      e.preventDefault();

      // Open sell form in new tab
      window.open('/sell-watch?shortcode=63863', '_blank');

      // Track sell interest
      if (typeof gtag === "function") {
        gtag("event", "sell_interest", {
          event_category: "engagement",
          event_label: "sell_button_click",
          page_url: pageUrl,
          value: 1
        });
      }

      if (typeof dataLayer !== "undefined") {
        dataLayer.push({
          event: "sell_interest",
          event_category: "engagement",
          event_action: "sell_button_click",
          event_label: pageUrl,
          page_url: pageUrl
        });
      }
    });
  }
});
</script>

