<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rolex Sapphire Evolution & Red Letters Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #004d66 0%, #0066cc 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 20px;
        }
        
        .styled-table {
            border-collapse: collapse;
            margin: 2em auto;
            font-family: Arial, sans-serif;
            width: 100%;
            max-width: 900px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }
        
        .styled-table thead {
            background: linear-gradient(135deg, #004d66 0%, #0066cc 100%);
            color: #fff;
            text-align: left;
        }
        
        .styled-table th,
        .styled-table td {
            padding: 12px 16px;
            text-align: left;
        }
        
        .styled-table th {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }
        
        .styled-table tbody tr {
            border-bottom: 1px solid #e8e8e8;
            transition: background-color 0.3s ease;
        }
        
        .styled-table tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }
        
        .styled-table tbody tr:hover {
            background-color: #e3f2fd;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }
        
        .styled-table tbody tr:last-of-type {
            border-bottom: none;
        }
        
        .table-section h3 {
            font-family: 'Segoe UI', sans-serif;
            color: #004d66;
            text-align: center;
            margin: 3em 0 1em 0;
            font-size: 2rem;
            position: relative;
        }
        
        .table-section h3:after {
            content: '';
            display: block;
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #004d66 0%, #0066cc 100%);
            margin: 10px auto;
            border-radius: 2px;
        }
        
        .red-letters {
            font-family: 'Segoe UI', sans-serif;
            max-width: 900px;
            margin: 3em auto;
            line-height: 1.6;
            background: #f8fafc;
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #004d66;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .red-letters h3 {
            color: #004d66;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .red-letters p {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .code-grid {
            display: flex;
            justify-content: center;
            gap: 0.8em;
            font-weight: bold;
            letter-spacing: 0.3em;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            color: #004d66;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .example-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #90caf9;
        }
        
        .example-box strong {
            color: #004d66;
            font-size: 1.1rem;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .styled-table {
                font-size: 0.9rem;
            }
            
            .styled-table th,
            .styled-table td {
                padding: 8px 10px;
            }
            
            .table-section h3 {
                font-size: 1.5rem;
            }
            
            .code-grid {
                gap: 0.5em;
                font-size: 1rem;
                flex-wrap: wrap;
            }
            
            .red-letters {
                padding: 20px;
                margin: 2em auto;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 30px 15px;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .styled-table {
                font-size: 0.8rem;
            }
            
            .styled-table th,
            .styled-table td {
                padding: 6px 8px;
            }
        }
        
        /* Animation for page load */
        .container {
            animation: fadeInUp 0.8s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Scroll animation for tables */
        .styled-table {
            animation: slideIn 1s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕰️ Rolex Reference Guide</h1>
            <p>Sapphire Evolution Timeline & Certificate Decoding</p>
        </div>
        
        <div class="content">
            <!-- Sapphire Evolution -->
            <div class="table-section">
                <h3>Sapphire Crystal Evolution</h3>
                <table class="styled-table">
                    <thead>
                        <tr>
                            <th>Model</th>
                            <th>Stainless</th>
                            <th>Rolessor</th>
                            <th>Gold</th>
                            <th>Platinum</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>Quartz Beta 21 Ref 5100</td><td>—</td><td>—</td><td>1970</td><td>—</td></tr>
                        <tr><td>Perpetual Ref. 1530, 1630, 1831</td><td>1975</td><td>1975</td><td>—</td><td>1975</td></tr>
                        <tr><td>Oysterquartz</td><td>1978</td><td>1978</td><td>1978</td><td>—</td></tr>
                        <tr><td>Air-King</td><td>1991</td><td>—</td><td>—</td><td>—</td></tr>
                        <tr><td>Perpetual</td><td>—</td><td>1991</td><td>1991</td><td>—</td></tr>
                        <tr><td>Date</td><td>1989</td><td>1989</td><td>1989</td><td>—</td></tr>
                        <tr><td>DateJust</td><td>1989</td><td>1989</td><td>1978</td><td>—</td></tr>
                        <tr><td>Turn-O-Graph<br><small>New Plexi Ref 16250</small></td><td>1989</td><td>1989</td><td>—</td><td>—</td></tr>
                        <tr><td>Day-Date</td><td>—</td><td>—</td><td>1978</td><td>1978</td></tr>
                        <tr><td>Submariner 14060</td><td>1990</td><td>—</td><td>—</td><td>—</td></tr>
                        <tr><td>Submariner Date</td><td>1981</td><td>1984 Ref. 16803</td><td>1981</td><td>—</td></tr>
                        <tr><td>Sea-Dweller</td><td>1981</td><td>—</td><td>—</td><td>—</td></tr>
                        <tr><td>GMT Master</td><td>1989</td><td>New Plexi Ref 1675/3, 16753</td><td>1981</td><td>—</td></tr>
                        <tr><td>GMT Master II</td><td>1984</td><td>1989</td><td>1989</td><td>—</td></tr>
                        <tr><td>Explorer</td><td>1990</td><td>—</td><td>—</td><td>—</td></tr>
                        <tr><td>Explorer II</td><td>1984</td><td>—</td><td>—</td><td>—</td></tr>
                        <tr><td>Cosmograph Daytona</td><td>1988</td><td>1988</td><td>1988</td><td>—</td></tr>
                    </tbody>
                </table>
            </div>

            <!-- Red Letters Explanation -->
            <div class="red-letters">
                <h3>🔴 Red Letters on U.S. Certificates</h3>
                <p>
                    Some older Rolex warranty certificates issued in the U.S. feature the word:
                </p>
                <div class="code-grid">R O L E X &nbsp;&nbsp; W A T C H</div>
                <p>with the digits:</p>
                <div class="code-grid">1 2 3 4 5 &nbsp;&nbsp; 6 7 8 9 0</div>
                <p>below each letter. To decode, match each red letter to its position number (1–10).</p>
                
                <div class="example-box">
                    <strong>📅 Example:</strong> <strong style="color: #d32f2f;">L R C C T</strong> → <strong>3 1998</strong> (i.e. March 19, 1998)
                </div>
            </div>
        </div>
    </div>
</body>
</html>
