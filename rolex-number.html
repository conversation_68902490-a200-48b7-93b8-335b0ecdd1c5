<style>
  .rolex-lookup-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 0 auto 50px;
  }
  .rolex-lookup-container h2 {
    color: #0a3d62;
    margin-bottom: 15px;
  }
  .rolex-lookup-input {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: stretch;
  }
  .rolex-lookup-input input {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
  }
  .rolex-lookup-input button {
    padding: 6px 12px;
    background: #0a3d62;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    min-width: 70px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  .rolex-lookup-input button:hover {
    background: #062a44;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  }
  .rolex-lookup-input button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }
  .rolex-reset-btn {
    padding: 6px 12px;
    background: #6c757d;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    min-width: 70px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  .rolex-reset-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  }
  .rolex-reset-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .rolex-lookup-container {
      padding: 20px;
      margin: 0 auto 30px;
    }
    .rolex-lookup-input {
      flex-direction: column;
      gap: 10px;
    }
    .rolex-lookup-input input {
      min-width: auto;
      width: 100%;
    }
    .rolex-lookup-input button,
    .rolex-reset-btn {
      width: 100%;
      min-width: auto;
    }
  }

  @media (max-width: 480px) {
    .rolex-lookup-container {
      padding: 15px;
    }
    .rolex-lookup-container h2 {
      font-size: 1.3rem;
      text-align: center;
    }
  }
  #rolexResult {
    font-size:1.1rem;
    color:#0a3d62;
    margin-bottom:20px;
  }
  .rolex-table-wrapper {
    overflow-x:auto;
    margin-top:30px;
  }
  .rolex-data-table {
    width:100%;
    border-collapse:collapse;
  }
  .rolex-data-table th {
    background:#0a3d62;
    color:#fff;
    padding:8px;
  }
  .rolex-data-table td {
    padding:8px;
    border:1px solid #e1e4e8;
    text-align:center;
  }
  .rolex-data-table tbody tr:nth-child(even) {
    background:#f7f9fa;
  }
</style>

<div class="rolex-lookup-container">
  <h2>Find Your Rolex Production Year</h2>
  <div class="rolex-lookup-input">
    <input type="text" id="serialInput" placeholder="Enter Serial Number or Year (e.g. 'G' or '2010')…">
    <button id="rolexLookupBtn">Search</button>
    <button id="rolexResetBtn" class="rolex-reset-btn">Show All</button>
  </div>
  <div id="rolexResult"></div>
  <div class="rolex-table-wrapper">
    <table class="rolex-data-table">
      <thead><tr><th>Serial Number</th><th>Year</th></tr></thead>
      <tbody></tbody>
    </table>
  </div>
</div>

<!-- Rolex Types Table -->
<div class="rolex-lookup-container">
  <h2>Rolex Model Types & Reference Numbers</h2>
  <div class="rolex-table-wrapper">
    <table class="rolex-data-table">
      <thead><tr><th>Rolex Type</th><th>Reference Numbers</th></tr></thead>
      <tbody>
        <tr><td>Submariner (no date)</td><td>55 & 140</td></tr>
        <tr><td>Submariner</td><td>16, 166 & 168</td></tr>
        <tr><td>Sea Dweller</td><td>16 & 166</td></tr>
        <tr><td>GMT Master</td><td>16, 65, 167</td></tr>
        <tr><td>GMT Master II</td><td>167, 1167</td></tr>
        <tr><td>Day-Date (President)</td><td>65, 66, 18, 180, 182 & 183</td></tr>
        <tr><td>Datejust</td><td>16 & 162</td></tr>
        <tr><td>Daytona Manual Wind</td><td>62</td></tr>
        <tr><td>Daytona Cosmograph</td><td>165, 1165</td></tr>
        <tr><td>Explorer II</td><td>165</td></tr>
        <tr><td>Oyster Perpetual</td><td>10, 140, 142</td></tr>
        <tr><td>Airking</td><td>55 & 140</td></tr>
        <tr><td>Date</td><td>15 & 150</td></tr>
        <tr><td>Oysterquartz Datejust</td><td>170</td></tr>
        <tr><td>Oysterquartz Day-Date</td><td>190</td></tr>
        <tr><td>Yachtmaster</td><td>166, 686 & 696</td></tr>
        <tr><td>Midsize Oyster Perp DJ</td><td>68, 682</td></tr>
        <tr><td>Ladies Oyster Perpetual</td><td>67, 671, 672</td></tr>
        <tr><td>Ladies Date</td><td>65, 69, 691 & 692</td></tr>
        <tr><td>Ladies Datejust</td><td>65, 69, 691 & 692</td></tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Material Table -->
<div class="rolex-lookup-container">
  <h2>Material Codes</h2>
  <div class="rolex-table-wrapper">
    <table class="rolex-data-table">
      <thead><tr><th>Material</th><th>Code</th></tr></thead>
      <tbody>
        <tr><td>Stainless</td><td>0</td></tr>
        <tr><td>Yellow Gold Filled</td><td>1</td></tr>
        <tr><td>White Gold Filled</td><td>2</td></tr>
        <tr><td>Stainless & Yellow Gold</td><td>3</td></tr>
        <tr><td>Stainless w/ 18k White Gold</td><td>4</td></tr>
        <tr><td>Gold Shell</td><td>5</td></tr>
        <tr><td>Platinum</td><td>6</td></tr>
        <tr><td>14k Yellow Gold</td><td>7</td></tr>
        <tr><td>18k Yellow Gold</td><td>8</td></tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Bezel Table -->
<div class="rolex-lookup-container">
  <h2>Bezel Types</h2>
  <div class="rolex-table-wrapper">
    <table class="rolex-data-table">
      <thead><tr><th>Bezel</th><th>Code</th></tr></thead>
      <tbody>
        <tr><td>Polished</td><td>0</td></tr>
        <tr><td>Engine Turned</td><td>1</td></tr>
        <tr><td>Engine Turned</td><td>2</td></tr>
        <tr><td>Fluted</td><td>3</td></tr>
        <tr><td>Hand-Crafted</td><td>4</td></tr>
        <tr><td>Pyramid</td><td>5</td></tr>
        <tr><td>Rotating Bezel</td><td>6</td></tr>
      </tbody>
    </table>
  </div>
</div>

<script>
(function(){
  // Data array
  const data = [
    { serial:'Random', year:2018 },{ serial:'Random', year:2017 },{ serial:'Random', year:2016 },
    { serial:'Random', year:2015 },{ serial:'Random', year:2014 },{ serial:'Random', year:2013 },
    { serial:'Random', year:2012 },{ serial:'Random', year:2011 },{ serial:'G', year:2010 },
    { serial:'V', year:2009 },{ serial:'M OR V', year:2008 },{ serial:'M OR Z', year:2007 },
    { serial:'D OR Z', year:2006 },{ serial:'D', year:2005 },{ serial:'F', year:2005 },
    { serial:'F', year:2004 },{ serial:'F', year:2003 },{ serial:'Y', year:2002 },
    { serial:'K OR Y', year:2001 },{ serial:'K,000,001', year:2000 },{ serial:'P,000,001', year:2000 },
    { serial:'A,000,001', year:1999 },{ serial:'U,932,144', year:1998 },{ serial:'U,000,001', year:1997 },
    { serial:'T,000,001', year:1996 },{ serial:'W,000,001', year:1995 },{ serial:'S,860,880', year:1994 },
    { serial:'S,000,001', year:1993 },{ serial:'C,000,001', year:1992 },{ serial:'N,000,001', year:1991 },
    { serial:'X,000,001', year:1991 },{ serial:'E,000,001', year:1990 },{ serial:'L,980,000', year:1989 },
    { serial:'R,598,200', year:1988 },{ serial:'R,000,001', year:1987 },{ serial:'9,400,000', year:1987 },
    { serial:'8,900,000', year:1986 },{ serial:'8,614,000', year:1985 },{ serial:'8,070,022', year:1984 },
    { serial:'7,400,000', year:1983 },{ serial:'7,100,000', year:1982 },{ serial:'6,520,870', year:1981 },
    { serial:'6,434,000', year:1980 },{ serial:'5,737,030', year:1979 },{ serial:'5,000,000', year:1978 },
    { serial:'5,008,000', year:1977 },{ serial:'4,115,299', year:1976 },{ serial:'3,862,196', year:1975 },
    { serial:'3,567,927', year:1974 },{ serial:'3,200,268', year:1973 },{ serial:'2,890,459', year:1972 },
    { serial:'2,589,295', year:1971 },{ serial:'2,241,882', year:1970 },{ serial:'1,900,000', year:1969 },
    { serial:'1,752,000', year:1968 },{ serial:'1,538,435', year:1967 },{ serial:'1,200,000', year:1966 },
    { serial:'1,100,000', year:1965 },{ serial:'1,008,889', year:1964 },{ serial:'824,000', year:1963 },
    { serial:'744,000', year:1962 },{ serial:'643,153', year:1961 },{ serial:'516,000', year:1960 },
    { serial:'399,453', year:1959 },{ serial:'328,000', year:1958 },{ serial:'224,000', year:1957 },
    { serial:'133,061', year:1956 },{ serial:'97,000', year:1955 },{ serial:'23,000', year:1954 },
    { serial:'855,726', year:1953 },{ serial:'726,639', year:1952 },{ serial:'709,249', year:1951 },
    { serial:'', year:1950 },{ serial:'', year:1949 },{ serial:'628,840', year:1948 },
    { serial:'529,163', year:1947 },{ serial:'367,946', year:1946 },{ serial:'302,459', year:1945 },
    { serial:'269,561', year:1944 },{ serial:'230,878', year:1943 },{ serial:'143,509', year:1942 },
    { serial:'106,047', year:1941 },{ serial:'99,775', year:1940 },{ serial:'71,224', year:1939 },
    { serial:'43,739', year:1938 },{ serial:'40,920', year:1937 },{ serial:'36,856', year:1936 },
    { serial:'34,336', year:1935 },{ serial:'30,823', year:1934 },{ serial:'29,562', year:1933 },
    { serial:'29,132', year:1932 },{ serial:'', year:1931 },{ serial:'23,186', year:1930 },
    { serial:'', year:1929 },{ serial:'23,969', year:1928 },{ serial:'20,190', year:1927 },
    { serial:'00,001', year:1926 }
  ];

  const tbody = document.querySelector('.rolex-data-table tbody');
  data.forEach(item => {
    const tr = document.createElement('tr');
    tr.innerHTML = `<td>${item.serial}</td><td>${item.year}</td>`;
    tbody.appendChild(tr);
  });

  const rows = Array.from(tbody.children);

  // Search function
  function performSearch() {
    const raw = document.getElementById('serialInput').value.trim();
    if (!raw) {
      rows.forEach(r => r.style.display = '');
      document.getElementById('rolexResult').textContent = '';
      return;
    }

    const year = parseInt(raw, 10);
    const isYearSearch = !isNaN(year);
    let count = 0;

    rows.forEach(r => {
      const rSerial = r.children[0].textContent.toLowerCase();
      const rYear = parseInt(r.children[1].textContent, 10);
      let match = false;

      if (isYearSearch) {
        // Search by year
        match = rYear === year;
      } else {
        // Search by serial number (case insensitive, partial match)
        match = rSerial.includes(raw.toLowerCase());
      }

      if (match) {
        r.style.display = '';
        count++;
      } else {
        r.style.display = 'none';
      }
    });

    if (count === 0) {
      document.getElementById('rolexResult').textContent = isYearSearch ?
        'No records found for this year.' :
        'No records found for this serial number.';
    } else {
      document.getElementById('rolexResult').textContent = '';
    }
  }

  // Reset function
  function resetSearch() {
    document.getElementById('serialInput').value = '';
    rows.forEach(r => r.style.display = '');
    document.getElementById('rolexResult').textContent = '';
  }

  // Event listeners
  document.getElementById('rolexLookupBtn').onclick = performSearch;
  document.getElementById('rolexResetBtn').onclick = resetSearch;

  // Enter key support
  document.getElementById('serialInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      performSearch();
    }
  });
})();
</script>
